import pandas as pd
import os

def create_sample_excel():
    """创建示例Excel文件用于测试"""
    
    # 示例数据
    sample_data = [
        {
            '搜索主图': '图片1.jpg',
            '商品ID': 'SP001',
            '搜索关键词': '狐狸肛塞尾巴女用玩具情趣肛门自慰器男同菊花前列腺按摩后庭拉珠',
            '单数': 10,
            '合计单量': 10,
            '需求备注': '高优先级'
        },
        {
            '搜索主图': '图片2.jpg',
            '商品ID': 'SP002',
            '搜索关键词': '情趣用品成人玩具女性自慰器震动棒',
            '单数': 15,
            '合计单量': 15,
            '需求备注': '中等优先级'
        },
        {
            '搜索主图': '图片3.jpg',
            '商品ID': 'SP003',
            '搜索关键词': '狐狸肛塞尾巴女用玩具情趣肛门自慰器男同菊花前列腺按摩后庭拉珠',
            '单数': 8,
            '合计单量': 8,
            '需求备注': '低优先级'
        },
        {
            '搜索主图': '图片4.jpg',
            '商品ID': 'SP004',
            '搜索关键词': '男性延时喷剂持久液',
            '单数': 12,
            '合计单量': 12,
            '需求备注': '紧急处理'
        },
        {
            '搜索主图': '图片5.jpg',
            '商品ID': 'SP005',
            '搜索关键词': '情趣用品成人玩具女性自慰器震动棒',
            '单数': 7,
            '合计单量': 7,
            '需求备注': '常规处理'
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(sample_data)
    
    # 创建示例文件目录
    sample_dir = 'sample_data'
    if not os.path.exists(sample_dir):
        os.makedirs(sample_dir)
    
    # 创建第一个示例文件（包含多个sheet）
    file1_path = os.path.join(sample_dir, '运营表格_示例1.xlsx')
    with pd.ExcelWriter(file1_path, engine='openpyxl') as writer:
        # Sheet1: 主要数据
        df.to_excel(writer, sheet_name='主要数据', index=False)
        
        # Sheet2: 补充数据
        additional_data = [
            {
                '搜索主图': '图片6.jpg',
                '商品ID': 'SP006',
                '搜索关键词': '润滑液人体润滑剂',
                '单数': 20,
                '合计单量': 20,
                '需求备注': '大批量'
            },
            {
                '搜索主图': '图片7.jpg',
                '商品ID': 'SP007',
                '搜索关键词': '男性延时喷剂持久液',
                '单数': 5,
                '合计单量': 5,
                '需求备注': '小批量测试'
            }
        ]
        df_additional = pd.DataFrame(additional_data)
        df_additional.to_excel(writer, sheet_name='补充数据', index=False)
    
    # 创建第二个示例文件
    file2_path = os.path.join(sample_dir, '运营表格_示例2.xlsx')
    more_data = [
        {
            '搜索主图': '图片8.jpg',
            '商品ID': 'SP008',
            '搜索关键词': '润滑液人体润滑剂',
            '单数': 25,
            '合计单量': 25,
            '需求备注': '热销产品'
        },
        {
            '搜索主图': '图片9.jpg',
            '商品ID': 'SP009',
            '搜索关键词': '情趣内衣性感睡衣',
            '单数': 18,
            '合计单量': 18,
            '需求备注': '新品推广'
        },
        {
            '搜索主图': '图片10.jpg',
            '商品ID': 'SP010',
            '搜索关键词': '狐狸肛塞尾巴女用玩具情趣肛门自慰器男同菊花前列腺按摩后庭拉珠',
            '单数': 6,
            '合计单量': 6,
            '需求备注': '补货'
        }
    ]
    df_more = pd.DataFrame(more_data)
    df_more.to_excel(file2_path, index=False)
    
    print(f"示例文件已创建:")
    print(f"1. {file1_path}")
    print(f"2. {file2_path}")
    print(f"\n数据汇总预览:")
    
    # 显示汇总信息
    all_data = sample_data + additional_data + more_data
    keyword_summary = {}
    
    for item in all_data:
        keyword = item['搜索关键词']
        quantity = item['单数']
        
        if keyword in keyword_summary:
            keyword_summary[keyword] += quantity
        else:
            keyword_summary[keyword] = quantity
    
    print("\n按关键词汇总的单数:")
    for keyword, total in keyword_summary.items():
        print(f"- {keyword}: {total}单")
    
    print(f"\n如果分配给5个客服，分配结果将是:")
    for keyword, total in keyword_summary.items():
        base = total // 5
        extra = total % 5
        distribution = [base + (1 if i < extra else 0) for i in range(5)]
        print(f"- {keyword}: {distribution}")

if __name__ == "__main__":
    create_sample_excel()
