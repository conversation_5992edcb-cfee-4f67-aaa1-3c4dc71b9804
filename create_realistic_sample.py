import pandas as pd
import os

def create_realistic_sample():
    """创建更真实的示例Excel文件，模拟实际运营表格"""
    
    # 真实的运营表格数据
    sample_data = [
        {
            '搜索主图': 'https://example.com/image1.jpg',
            '商品ID': '9592414199',
            '搜索关键词': '狐狸肛塞尾巴女用玩具情趣肛门自慰器男同菊花前列腺按摩后庭拉珠',
            '单数': 2,
            '合计单量': 50,
            '需求备注': '高优先级产品，需要重点推广'
        },
        {
            '搜索主图': 'https://example.com/image2.jpg', 
            '商品ID': '9592414200',
            '搜索关键词': '情趣用品成人玩具女性自慰器震动棒',
            '单数': 5,
            '合计单量': 30,
            '需求备注': '新品上市，测试市场反应'
        },
        {
            '搜索主图': 'https://example.com/image3.jpg',
            '商品ID': '9592414201', 
            '搜索关键词': '狐狸肛塞尾巴女用玩具情趣肛门自慰器男同菊花前列腺按摩后庭拉珠',
            '单数': 8,
            '合计单量': 50,
            '需求备注': '同款产品，不同规格'
        },
        {
            '搜索主图': 'https://example.com/image4.jpg',
            '商品ID': '9592414202',
            '搜索关键词': '男性延时喷剂持久液成人用品',
            '单数': 12,
            '合计单量': 80,
            '需求备注': '热销产品，库存充足'
        },
        {
            '搜索主图': 'https://example.com/image5.jpg',
            '商品ID': '9592414203',
            '搜索关键词': '情趣用品成人玩具女性自慰器震动棒',
            '单数': 3,
            '合计单量': 30,
            '需求备注': '补充库存'
        },
        {
            '搜索主图': 'https://example.com/image6.jpg',
            '商品ID': '9592414204',
            '搜索关键词': '润滑液人体润滑剂水溶性',
            '单数': 15,
            '合计单量': 100,
            '需求备注': '基础必需品，持续推广'
        },
        {
            '搜索主图': 'https://example.com/image7.jpg',
            '商品ID': '9592414205',
            '搜索关键词': '情趣内衣性感睡衣透明诱惑',
            '单数': 7,
            '合计单量': 25,
            '需求备注': '季节性产品'
        }
    ]
    
    # 创建示例文件目录
    sample_dir = 'sample_data'
    if not os.path.exists(sample_dir):
        os.makedirs(sample_dir)
    
    # 创建真实运营表格示例
    file_path = os.path.join(sample_dir, '真实运营表格示例.xlsx')
    df = pd.DataFrame(sample_data)
    
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='运营数据', index=False)
    
    print(f"真实运营表格示例已创建: {file_path}")
    print("\n数据预览:")
    print("=" * 80)
    
    # 显示数据预览
    for i, row in enumerate(sample_data, 1):
        print(f"{i}. 商品ID: {row['商品ID']}")
        print(f"   搜索关键词: {row['搜索关键词']}")
        print(f"   单数: {row['单数']} (只有这个会被拆分)")
        print(f"   合计单量: {row['合计单量']} (保持原样)")
        print(f"   需求备注: {row['需求备注']} (保持原样)")
        print()
    
    # 汇总分析
    print("关键词汇总分析:")
    print("=" * 80)
    
    keyword_summary = {}
    for row in sample_data:
        keyword = row['搜索关键词']
        quantity = row['单数']
        
        if keyword in keyword_summary:
            keyword_summary[keyword] += quantity
        else:
            keyword_summary[keyword] = quantity
    
    for keyword, total in keyword_summary.items():
        print(f"关键词: {keyword}")
        print(f"总单数: {total}")
        print(f"分配给5个客服: {distribute_quantity(total, 5)}")
        print("-" * 40)

def distribute_quantity(total_quantity: int, num_recipients: int) -> list:
    """分配数量的辅助函数"""
    if num_recipients <= 0:
        return []
    
    if total_quantity <= 0:
        return [0] * num_recipients
    
    base_quantity = total_quantity // num_recipients
    remainder = total_quantity % num_recipients
    
    quantities = [base_quantity] * num_recipients
    for i in range(remainder):
        quantities[i] += 1
    
    return quantities

if __name__ == "__main__":
    create_realistic_sample()
    
    print("\n" + "=" * 80)
    print("重要说明:")
    print("1. 程序只会拆分 '搜索关键词' 和 '单数' 这两列")
    print("2. 其他列（搜索主图、商品ID、合计单量、需求备注）完全保持原样")
    print("3. 相同搜索关键词的单数会被汇总，然后平均分配给客服")
    print("4. 每个客服的文件中，除了单数被重新分配外，其他数据都是原始数据")
    print("=" * 80)
