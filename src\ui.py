"""
UI界面模块
包含PyQt6界面组件和主窗口类
"""

import os
from typing import List
from PyQt6.QtWidgets import (QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QFileDialog, QTextEdit,
                             QListWidget, QMessageBox, QProgressBar,
                             QGroupBox, QGridLayout, QLineEdit)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from .config import Config, Messages, Paths
from .data_processor import DataProcessor
from .utils import ValidationUtils, FileUtils


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.file_paths = []
        self.customer_service_names = []
        self.output_dir = ""
        self.processor = None
        self.init_ui()
        self.load_default_settings()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(Config.APP_NAME)
        self.setGeometry(100, 100, Config.WINDOW_WIDTH, Config.WINDOW_HEIGHT)
        self.setMinimumSize(Config.WINDOW_MIN_WIDTH, Config.WINDOW_MIN_HEIGHT)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 添加各个组件
        self._create_title(main_layout)
        self._create_file_section(main_layout)
        self._create_customer_section(main_layout)
        self._create_output_section(main_layout)
        self._create_process_section(main_layout)
        self._create_status_section(main_layout)
    
    def _create_title(self, main_layout):
        """创建标题"""
        title_label = QLabel(Config.APP_NAME)
        title_label.setFont(QFont("Arial", Config.TITLE_FONT_SIZE, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
    
    def _create_file_section(self, main_layout):
        """创建文件选择区域"""
        file_group = QGroupBox("1. 选择Excel文件")
        file_layout = QVBoxLayout(file_group)
        
        # 按钮布局
        file_button_layout = QHBoxLayout()
        self.select_files_btn = QPushButton(Messages.UI_SELECT_FILES)
        self.select_files_btn.clicked.connect(self.select_files)
        file_button_layout.addWidget(self.select_files_btn)
        
        self.clear_files_btn = QPushButton(Messages.UI_CLEAR_FILES)
        self.clear_files_btn.clicked.connect(self.clear_files)
        file_button_layout.addWidget(self.clear_files_btn)
        file_layout.addLayout(file_button_layout)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(Config.LIST_MAX_HEIGHT)
        file_layout.addWidget(self.file_list)
        
        main_layout.addWidget(file_group)
    
    def _create_customer_section(self, main_layout):
        """创建客服设置区域"""
        customer_group = QGroupBox("2. 客服设置")
        customer_layout = QGridLayout(customer_group)
        
        # 客服输入
        customer_layout.addWidget(QLabel("客服姓名:"), 0, 0)
        self.customer_input = QLineEdit()
        self.customer_input.setPlaceholderText(Messages.UI_CUSTOMER_NAME_PLACEHOLDER)
        self.customer_input.returnPressed.connect(self.add_customer)
        customer_layout.addWidget(self.customer_input, 0, 1)
        
        self.add_customer_btn = QPushButton(Messages.UI_ADD_CUSTOMER)
        self.add_customer_btn.clicked.connect(self.add_customer)
        customer_layout.addWidget(self.add_customer_btn, 0, 2)
        
        # 客服列表
        self.customer_list = QListWidget()
        self.customer_list.setMaximumHeight(Config.LIST_MAX_HEIGHT)
        customer_layout.addWidget(self.customer_list, 1, 0, 1, 3)
        
        # 客服操作按钮
        remove_customer_layout = QHBoxLayout()
        self.remove_customer_btn = QPushButton(Messages.UI_REMOVE_CUSTOMER)
        self.remove_customer_btn.clicked.connect(self.remove_customer)
        remove_customer_layout.addWidget(self.remove_customer_btn)
        
        self.clear_customers_btn = QPushButton(Messages.UI_CLEAR_CUSTOMERS)
        self.clear_customers_btn.clicked.connect(self.clear_customers)
        remove_customer_layout.addWidget(self.clear_customers_btn)
        
        customer_layout.addLayout(remove_customer_layout, 2, 0, 1, 3)
        
        main_layout.addWidget(customer_group)
    
    def _create_output_section(self, main_layout):
        """创建输出设置区域"""
        output_group = QGroupBox("3. 输出设置")
        output_layout = QHBoxLayout(output_group)
        
        output_layout.addWidget(QLabel("输出目录:"))
        self.output_label = QLabel(Messages.UI_OUTPUT_NOT_SELECTED)
        self.output_label.setStyleSheet("QLabel { border: 1px solid gray; padding: 5px; }")
        output_layout.addWidget(self.output_label)
        
        self.select_output_btn = QPushButton(Messages.UI_SELECT_OUTPUT_DIR)
        self.select_output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(self.select_output_btn)
        
        main_layout.addWidget(output_group)
    
    def _create_process_section(self, main_layout):
        """创建处理按钮和进度条"""
        # 处理按钮
        self.process_btn = QPushButton(Messages.UI_START_PROCESSING)
        self.process_btn.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setMinimumHeight(Config.BUTTON_MIN_HEIGHT)
        main_layout.addWidget(self.process_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
    
    def _create_status_section(self, main_layout):
        """创建状态显示区域"""
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(Config.STATUS_TEXT_MAX_HEIGHT)
        self.status_text.setReadOnly(True)
        main_layout.addWidget(self.status_text)
    
    def load_default_settings(self):
        """加载默认设置"""
        # 添加默认客服
        for customer in Config.DEFAULT_CUSTOMERS:
            self.customer_service_names.append(customer)
            self.customer_list.addItem(customer)

        # 设置默认输出目录
        default_output = Paths.get_default_output_dir()
        self.output_dir = default_output
        self.output_label.setText(default_output)

    # 文件操作方法
    def select_files(self):
        """选择Excel文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            Messages.DIALOG_SELECT_FILES,
            "",
            "Excel files (*.xlsx *.xls)"
        )

        new_files = []
        for file in files:
            if file not in self.file_paths:
                # 验证文件
                is_valid, message = FileUtils.validate_excel_file(file)
                if is_valid:
                    self.file_paths.append(file)
                    self.file_list.addItem(os.path.basename(file))
                    new_files.append(file)
                else:
                    self.show_warning(f"文件 {os.path.basename(file)} 无效: {message}")

        if new_files:
            self.update_status(Messages.SUCCESS_FILES_SELECTED.format(len(new_files)))

    def clear_files(self):
        """清空文件列表"""
        self.file_paths.clear()
        self.file_list.clear()
        self.update_status("已清空文件列表")

    # 客服操作方法
    def add_customer(self):
        """添加客服"""
        customer_name = self.customer_input.text().strip()

        # 验证客服名称
        is_valid, message = ValidationUtils.validate_customer_name(customer_name)
        if not is_valid:
            self.show_warning(message)
            return

        # 检查是否已存在
        if customer_name in self.customer_service_names:
            self.show_warning(Messages.ERROR_CUSTOMER_EXISTS.format(customer_name))
            return

        # 添加客服
        self.customer_service_names.append(customer_name)
        self.customer_list.addItem(customer_name)
        self.customer_input.clear()
        self.update_status(Messages.SUCCESS_CUSTOMER_ADDED.format(customer_name))

    def remove_customer(self):
        """删除选中的客服"""
        current_row = self.customer_list.currentRow()
        if current_row >= 0:
            item = self.customer_list.takeItem(current_row)
            customer_name = item.text()
            self.customer_service_names.remove(customer_name)
            self.update_status(f"已删除客服: {customer_name}")

    def clear_customers(self):
        """清空客服列表"""
        self.customer_service_names.clear()
        self.customer_list.clear()
        self.update_status("已清空客服列表")

    # 输出目录操作
    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            Messages.DIALOG_SELECT_OUTPUT_DIR
        )

        if dir_path:
            # 验证输出目录
            is_valid, message = ValidationUtils.validate_output_directory(dir_path)
            if is_valid:
                self.output_dir = dir_path
                self.output_label.setText(dir_path)
                self.update_status(f"已选择输出目录: {dir_path}")
            else:
                self.show_error(message)

    # 处理操作
    def start_processing(self):
        """开始处理数据"""
        # 验证输入
        validation_result = self._validate_inputs()
        if not validation_result[0]:
            self.show_warning(validation_result[1])
            return

        # 显示处理前的统计信息
        self._show_processing_preview()

        # 开始处理
        self._start_data_processing()

    def _validate_inputs(self) -> tuple:
        """验证所有输入"""
        # 验证文件
        if not self.file_paths:
            return False, Messages.ERROR_NO_FILES_SELECTED

        # 验证客服
        if not self.customer_service_names:
            return False, Messages.ERROR_NO_CUSTOMERS

        # 验证输出目录
        if not self.output_dir:
            return False, Messages.ERROR_NO_OUTPUT_DIR

        # 验证文件列表
        is_valid, message, invalid_files = ValidationUtils.validate_file_list(self.file_paths)
        if not is_valid:
            return False, message

        if invalid_files:
            # 显示警告但继续处理
            self.update_status(f"警告: {message}")
            for invalid_file in invalid_files:
                self.update_status(f"  - {invalid_file}")

        return True, "验证通过"

    def _show_processing_preview(self):
        """显示处理预览信息"""
        file_count = len(self.file_paths)
        customer_count = len(self.customer_service_names)

        preview_text = (
            f"准备开始处理:\n"
            f"- 文件数量: {file_count}\n"
            f"- 客服数量: {customer_count}\n"
            f"- 输出目录: {self.output_dir}\n"
            f"客服列表: {', '.join(self.customer_service_names)}"
        )

        self.update_status(preview_text)

    def _start_data_processing(self):
        """启动数据处理线程"""
        # 禁用界面控件
        self._set_ui_enabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 清空状态文本
        self.status_text.clear()

        # 创建并启动处理线程
        self.processor = DataProcessor(
            self.file_paths,
            self.customer_service_names,
            self.output_dir
        )

        # 连接信号
        self.processor.progress_updated.connect(self.progress_bar.setValue)
        self.processor.status_updated.connect(self.update_status)
        self.processor.finished_signal.connect(self.processing_finished)
        self.processor.error_signal.connect(self.processing_error)

        # 启动线程
        self.processor.start()

    def _set_ui_enabled(self, enabled: bool):
        """设置UI控件的启用状态"""
        self.select_files_btn.setEnabled(enabled)
        self.clear_files_btn.setEnabled(enabled)
        self.add_customer_btn.setEnabled(enabled)
        self.remove_customer_btn.setEnabled(enabled)
        self.clear_customers_btn.setEnabled(enabled)
        self.select_output_btn.setEnabled(enabled)
        self.process_btn.setEnabled(enabled)
        self.customer_input.setEnabled(enabled)

    # 处理结果回调方法
    def update_status(self, message: str):
        """更新状态显示"""
        self.status_text.append(message)
        self.status_text.ensureCursorVisible()

    def processing_finished(self, message: str):
        """处理完成回调"""
        self.update_status(message)
        self._set_ui_enabled(True)
        self.progress_bar.setVisible(False)
        self.show_success(message)

    def processing_error(self, error_message: str):
        """处理错误回调"""
        self.update_status(f"错误: {error_message}")
        self._set_ui_enabled(True)
        self.progress_bar.setVisible(False)
        self.show_error(error_message)

    # 消息对话框方法
    def show_success(self, message: str):
        """显示成功消息"""
        QMessageBox.information(self, Messages.DIALOG_SUCCESS, message)

    def show_warning(self, message: str):
        """显示警告消息"""
        QMessageBox.warning(self, Messages.DIALOG_WARNING, message)

    def show_error(self, message: str):
        """显示错误消息"""
        QMessageBox.critical(self, Messages.DIALOG_ERROR, message)

    def show_info(self, message: str):
        """显示信息消息"""
        QMessageBox.information(self, Messages.DIALOG_INFO, message)
