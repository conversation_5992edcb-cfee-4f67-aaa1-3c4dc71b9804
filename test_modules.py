"""
模块化测试脚本
验证各个模块是否能正确导入和工作
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from src.config import Config, Messages, Paths
        print("✓ 配置模块导入成功")
    except ImportError as e:
        print(f"✗ 配置模块导入失败: {e}")
        return False
    
    try:
        from src.utils import FileUtils, DataUtils, ValidationUtils
        print("✓ 工具模块导入成功")
    except ImportError as e:
        print(f"✗ 工具模块导入失败: {e}")
        return False
    
    try:
        from src.data_processor import DataProcessor, ExcelFileReader, DataAggregator
        print("✓ 数据处理模块导入成功")
    except ImportError as e:
        print(f"✗ 数据处理模块导入失败: {e}")
        return False
    
    try:
        from src.ui import MainWindow
        print("✓ UI模块导入成功")
    except ImportError as e:
        print(f"✗ UI模块导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置模块"""
    print("\n测试配置模块...")
    
    from src.config import Config, Messages, Paths
    
    # 测试配置项
    assert Config.APP_NAME == "运营表格数据自动拆分工具"
    assert len(Config.REQUIRED_COLUMNS) == 6
    assert len(Config.DEFAULT_CUSTOMERS) == 5
    print("✓ 配置项正确")
    
    # 测试文件验证
    assert Config.validate_file_extension("test.xlsx") == True
    assert Config.validate_file_extension("test.txt") == False
    print("✓ 文件扩展名验证正确")
    
    # 测试路径
    output_dir = Paths.get_default_output_dir()
    assert os.path.exists(output_dir)
    print("✓ 默认输出目录创建成功")

def test_utils():
    """测试工具模块"""
    print("\n测试工具模块...")
    
    from src.utils import DataUtils, ValidationUtils, FileUtils
    
    # 测试数量分配
    result = DataUtils.distribute_quantity(10, 3)
    assert result == [4, 3, 3]
    print("✓ 数量分配算法正确")
    
    # 测试客服名称验证
    is_valid, msg = ValidationUtils.validate_customer_name("客服A")
    assert is_valid == True
    print("✓ 客服名称验证正确")
    
    # 测试无效客服名称
    is_valid, msg = ValidationUtils.validate_customer_name("")
    assert is_valid == False
    print("✓ 无效客服名称验证正确")

def test_data_processor():
    """测试数据处理模块"""
    print("\n测试数据处理模块...")
    
    from src.data_processor import ExcelFileReader, DataAggregator
    
    # 测试数据汇总器
    aggregator = DataAggregator()
    
    # 模拟数据
    test_data = [
        {
            'file_path': 'test1.xlsx',
            'sheet_name': 'Sheet1',
            'data': {'搜索关键词': '关键词1', '单数': 5}
        },
        {
            'file_path': 'test1.xlsx',
            'sheet_name': 'Sheet1',
            'data': {'搜索关键词': '关键词1', '单数': 3}
        },
        {
            'file_path': 'test2.xlsx',
            'sheet_name': 'Sheet1',
            'data': {'搜索关键词': '关键词2', '单数': 10}
        },
    ]
    
    result = aggregator.aggregate_data(test_data)
    assert '关键词1' in result
    assert result['关键词1']['total_quantity'] == 8
    assert result['关键词2']['total_quantity'] == 10
    print("✓ 数据汇总功能正确")
    
    # 测试数据分配
    customer_names = ['客服A', '客服B', '客服C']
    customer_data = aggregator.distribute_data(result, customer_names)
    assert len(customer_data) == 3
    print("✓ 数据分配功能正确")

def main():
    """主测试函数"""
    print("开始模块化测试...\n")
    
    # 测试导入
    if not test_imports():
        print("\n模块导入测试失败，请检查代码")
        return False
    
    try:
        # 测试各个模块
        test_config()
        test_utils()
        test_data_processor()
        
        print("\n🎉 所有测试通过！模块化重构成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
