"""
配置管理模块
包含程序的配置项和常量定义
"""

import os
from typing import List

class Config:
    """程序配置类"""
    
    # 应用程序信息
    APP_NAME = "运营表格数据自动拆分工具"
    APP_VERSION = "1.0.0"
    
    # 窗口设置
    WINDOW_WIDTH = 800
    WINDOW_HEIGHT = 600
    WINDOW_MIN_WIDTH = 600
    WINDOW_MIN_HEIGHT = 400
    
    # 文件设置
    SUPPORTED_FILE_EXTENSIONS = [".xlsx", ".xls"]
    MAX_FILE_SIZE_MB = 100
    
    # Excel列名配置
    REQUIRED_COLUMNS = [
        "搜索主图",
        "商品ID", 
        "搜索关键词",
        "单数",
        "合计单量",
        "需求备注"
    ]
    
    # 默认客服列表
    DEFAULT_CUSTOMERS = [
        "客服A",
        "客服B", 
        "客服C",
        "客服D",
        "客服E"
    ]
    
    # UI样式
    TITLE_FONT_SIZE = 16
    BUTTON_MIN_HEIGHT = 40
    LIST_MAX_HEIGHT = 100
    STATUS_TEXT_MAX_HEIGHT = 150
    
    # 处理设置
    PROGRESS_UPDATE_INTERVAL = 100  # 毫秒
    MAX_THREADS = 1  # 处理线程数
    
    # 输出文件设置
    OUTPUT_FILE_SUFFIX = "_运营表格.xlsx"
    BACKUP_ENABLED = True
    
    @classmethod
    def get_output_filename(cls, customer_name: str) -> str:
        """生成输出文件名"""
        return f"{customer_name}{cls.OUTPUT_FILE_SUFFIX}"
    
    @classmethod
    def validate_file_extension(cls, file_path: str) -> bool:
        """验证文件扩展名"""
        _, ext = os.path.splitext(file_path.lower())
        return ext in cls.SUPPORTED_FILE_EXTENSIONS
    
    @classmethod
    def get_file_size_mb(cls, file_path: str) -> float:
        """获取文件大小（MB）"""
        try:
            size_bytes = os.path.getsize(file_path)
            return size_bytes / (1024 * 1024)
        except OSError:
            return 0.0
    
    @classmethod
    def validate_file_size(cls, file_path: str) -> bool:
        """验证文件大小"""
        return cls.get_file_size_mb(file_path) <= cls.MAX_FILE_SIZE_MB


class Messages:
    """消息文本常量"""
    
    # 成功消息
    SUCCESS_PROCESSING_COMPLETE = "处理完成！文件已保存到: {}"
    SUCCESS_FILES_SELECTED = "已选择 {} 个文件"
    SUCCESS_CUSTOMER_ADDED = "客服 '{}' 已添加"
    
    # 错误消息
    ERROR_NO_FILES_SELECTED = "请选择至少一个Excel文件！"
    ERROR_NO_CUSTOMERS = "请添加至少一个客服！"
    ERROR_NO_OUTPUT_DIR = "请选择输出目录！"
    ERROR_FILE_READ = "读取文件 {} 时出错: {}"
    ERROR_MISSING_COLUMNS = "文件 {} 的工作表 '{}' 缺少必需的列: {}"
    ERROR_PROCESSING = "处理过程中出现错误: {}"
    ERROR_FILE_SIZE_TOO_LARGE = "文件 {} 太大（{:.1f}MB），超过限制（{}MB）"
    ERROR_INVALID_FILE_FORMAT = "文件 {} 格式不支持"
    ERROR_CUSTOMER_EXISTS = "客服 '{}' 已存在"
    ERROR_CUSTOMER_NAME_EMPTY = "客服名称不能为空"
    
    # 警告消息
    WARNING_MISSING_COLUMNS = "警告: {} 缺少列: {}"
    WARNING_COPY_FORMAT = "复制格式时出现警告: {}"
    WARNING_NO_VALID_DATA = "警告: 没有找到有效的数据行"
    
    # 状态消息
    STATUS_READING_FILES = "开始读取文件..."
    STATUS_READING_FILE = "正在读取文件 {}/{}: {}"
    STATUS_PROCESSING_SHEET = "正在处理 {} 工作表..."
    STATUS_SUMMARIZING = "开始汇总和拆分数据..."
    STATUS_ASSIGNING = "开始分配给客服..."
    STATUS_GENERATING = "正在生成输出文件..."
    STATUS_COPYING_FORMAT = "正在复制格式..."
    
    # 界面文本
    UI_SELECT_FILES = "选择Excel文件"
    UI_CLEAR_FILES = "清空文件列表"
    UI_ADD_CUSTOMER = "添加客服"
    UI_REMOVE_CUSTOMER = "删除选中客服"
    UI_CLEAR_CUSTOMERS = "清空客服列表"
    UI_SELECT_OUTPUT_DIR = "选择输出目录"
    UI_START_PROCESSING = "开始处理"
    UI_CUSTOMER_NAME_PLACEHOLDER = "输入客服姓名，按回车添加"
    UI_OUTPUT_NOT_SELECTED = "未选择"
    
    # 对话框标题
    DIALOG_SELECT_FILES = "选择Excel文件"
    DIALOG_SELECT_OUTPUT_DIR = "选择输出目录"
    DIALOG_WARNING = "警告"
    DIALOG_ERROR = "错误"
    DIALOG_SUCCESS = "完成"
    DIALOG_INFO = "信息"


class Paths:
    """路径配置"""
    
    # 项目根目录
    PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 源代码目录
    SRC_DIR = os.path.join(PROJECT_ROOT, "src")
    
    # 示例数据目录
    SAMPLE_DATA_DIR = os.path.join(PROJECT_ROOT, "sample_data")
    
    # 输出目录（默认）
    DEFAULT_OUTPUT_DIR = os.path.join(PROJECT_ROOT, "output")
    
    # 日志目录
    LOG_DIR = os.path.join(PROJECT_ROOT, "logs")
    
    @classmethod
    def ensure_dir_exists(cls, dir_path: str) -> None:
        """确保目录存在"""
        os.makedirs(dir_path, exist_ok=True)
    
    @classmethod
    def get_default_output_dir(cls) -> str:
        """获取默认输出目录"""
        cls.ensure_dir_exists(cls.DEFAULT_OUTPUT_DIR)
        return cls.DEFAULT_OUTPUT_DIR
