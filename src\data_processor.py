"""
数据处理模块
负责Excel文件读取、数据汇总和分配逻辑
"""

import os
import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border
from openpyxl.utils.dataframe import dataframe_to_rows
from collections import defaultdict
from typing import List, Dict, Any, Tuple, Optional
from PyQt6.QtCore import QThread, pyqtSignal

from .config import Config, Messages
from .utils import FileUtils, DataUtils


class DataProcessor(QThread):
    """数据处理线程类"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(str)
    error_signal = pyqtSignal(str)
    
    def __init__(self, file_paths: List[str], customer_service_names: List[str], output_dir: str):
        super().__init__()
        self.file_paths = file_paths
        self.customer_service_names = customer_service_names
        self.output_dir = output_dir
        self.file_reader = ExcelFileReader()
        self.data_aggregator = DataAggregator()
        self.file_generator = OutputFileGenerator()
        
    def run(self):
        """运行数据处理"""
        try:
            self.process_files()
        except Exception as e:
            self.error_signal.emit(Messages.ERROR_PROCESSING.format(str(e)))
    
    def process_files(self):
        """处理文件的主要流程"""
        # 1. 读取文件
        self.status_updated.emit(Messages.STATUS_READING_FILES)
        all_data, file_info = self.file_reader.read_files(
            self.file_paths, 
            self.status_updated, 
            self.progress_updated
        )
        
        if not all_data:
            self.error_signal.emit("没有读取到有效数据")
            return
        
        # 2. 汇总数据
        self.status_updated.emit(Messages.STATUS_SUMMARIZING)
        keyword_summary = self.data_aggregator.aggregate_data(all_data)
        
        # 3. 分配数据
        self.status_updated.emit(Messages.STATUS_ASSIGNING)
        customer_data = self.data_aggregator.distribute_data(
            keyword_summary, 
            self.customer_service_names
        )
        
        self.progress_updated.emit(75)
        
        # 4. 生成输出文件
        self.status_updated.emit(Messages.STATUS_GENERATING)
        self.file_generator.generate_files(
            customer_data, 
            file_info, 
            self.customer_service_names,
            self.output_dir,
            self.status_updated
        )
        
        self.progress_updated.emit(100)
        self.finished_signal.emit(Messages.SUCCESS_PROCESSING_COMPLETE.format(self.output_dir))


class ExcelFileReader:
    """Excel文件读取器"""
    
    def read_files(self, file_paths: List[str], status_callback, progress_callback) -> Tuple[List[Dict], List[Dict]]:
        """读取多个Excel文件"""
        all_data = []
        file_info = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            status_callback.emit(Messages.STATUS_READING_FILE.format(
                i+1, total_files, os.path.basename(file_path)
            ))
            
            try:
                file_data, file_sheets = self._read_single_file(file_path, status_callback)
                all_data.extend(file_data)
                file_info.extend(file_sheets)
                
            except Exception as e:
                status_callback.emit(Messages.ERROR_FILE_READ.format(file_path, str(e)))
                continue
            
            progress = int((i + 1) / total_files * 50)
            progress_callback.emit(progress)
        
        return all_data, file_info
    
    def _read_single_file(self, file_path: str, status_callback) -> Tuple[List[Dict], List[Dict]]:
        """读取单个Excel文件"""
        file_data = []
        file_sheets = []
        
        # 验证文件
        if not Config.validate_file_extension(file_path):
            raise ValueError(Messages.ERROR_INVALID_FILE_FORMAT.format(file_path))
        
        if not Config.validate_file_size(file_path):
            size_mb = Config.get_file_size_mb(file_path)
            raise ValueError(Messages.ERROR_FILE_SIZE_TOO_LARGE.format(
                file_path, size_mb, Config.MAX_FILE_SIZE_MB
            ))
        
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        
        for sheet_name in excel_file.sheet_names:
            status_callback.emit(Messages.STATUS_PROCESSING_SHEET.format(sheet_name))
            
            # 读取工作表数据
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 检查必要的列
            missing_columns = [col for col in Config.REQUIRED_COLUMNS if col not in df.columns]
            if missing_columns:
                status_callback.emit(Messages.ERROR_MISSING_COLUMNS.format(
                    os.path.basename(file_path), sheet_name, missing_columns
                ))
                continue
            
            # 存储数据
            for idx, row in df.iterrows():
                file_data.append({
                    'file_path': file_path,
                    'sheet_name': sheet_name,
                    'row_index': idx,
                    'data': row.to_dict(),
                    'original_df': df
                })
            
            file_sheets.append({
                'file_path': file_path,
                'sheet_name': sheet_name,
                'df': df
            })
        
        return file_data, file_sheets


class DataAggregator:
    """数据汇总器"""
    
    def aggregate_data(self, all_data: List[Dict]) -> Dict[str, Dict]:
        """汇总相同搜索关键词的数据"""
        keyword_summary = defaultdict(lambda: {'total_quantity': 0, 'rows': []})
        
        for item in all_data:
            keyword = item['data'].get('搜索关键词', '')
            quantity = item['data'].get('单数', 0)
            
            if pd.notna(keyword) and pd.notna(quantity) and quantity > 0:
                keyword_summary[keyword]['total_quantity'] += int(quantity)
                keyword_summary[keyword]['rows'].append(item)
        
        return dict(keyword_summary)
    
    def distribute_data(self, keyword_summary: Dict[str, Dict], customer_names: List[str]) -> Dict[str, Dict]:
        """将数据分配给客服"""
        customer_data = {name: defaultdict(list) for name in customer_names}
        
        for keyword, summary in keyword_summary.items():
            total_quantity = summary['total_quantity']
            num_customers = len(customer_names)
            
            # 计算分配数量
            quantities = DataUtils.distribute_quantity(total_quantity, num_customers)
            
            # 为每个客服分配数据
            for i, customer_name in enumerate(customer_names):
                if quantities[i] > 0:
                    # 复制第一行数据作为模板
                    template_row = summary['rows'][0].copy()
                    template_row['data'] = template_row['data'].copy()
                    template_row['data']['单数'] = quantities[i]
                    
                    file_key = f"{template_row['file_path']}_{template_row['sheet_name']}"
                    customer_data[customer_name][file_key].append(template_row)
        
        return customer_data


class OutputFileGenerator:
    """输出文件生成器"""
    
    def generate_files(self, customer_data: Dict[str, Dict], file_info: List[Dict], 
                      customer_names: List[str], output_dir: str, status_callback):
        """为每个客服生成Excel文件"""
        for customer_name in customer_names:
            self._create_customer_file(
                customer_name, 
                customer_data[customer_name], 
                file_info, 
                output_dir,
                status_callback
            )
    
    def _create_customer_file(self, customer_name: str, customer_data: Dict, 
                             file_info: List[Dict], output_dir: str, status_callback):
        """为单个客服创建Excel文件"""
        output_file = os.path.join(output_dir, Config.get_output_filename(customer_name))
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            for file_sheet_key, rows in customer_data.items():
                if not rows:
                    continue
                
                # 获取原始文件信息
                original_info = self._find_original_info(file_sheet_key, file_info)
                if not original_info:
                    continue
                
                # 创建新的DataFrame
                new_df = self._create_customer_dataframe(original_info['df'], rows)
                
                # 写入Excel
                sheet_name = original_info['sheet_name']
                new_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 复制原始格式
                self._copy_formatting(
                    writer, sheet_name, 
                    original_info['file_path'], 
                    original_info['sheet_name'],
                    status_callback
                )
    
    def _find_original_info(self, file_sheet_key: str, file_info: List[Dict]) -> Optional[Dict]:
        """查找原始文件信息"""
        for info in file_info:
            if f"{info['file_path']}_{info['sheet_name']}" == file_sheet_key:
                return info
        return None
    
    def _create_customer_dataframe(self, original_df: pd.DataFrame, rows: List[Dict]) -> pd.DataFrame:
        """为客服创建DataFrame"""
        # 创建新的DataFrame，只保留表头
        new_df = original_df.iloc[:0].copy()
        
        # 添加客服的数据
        for row_data in rows:
            new_df = pd.concat([new_df, pd.DataFrame([row_data['data']])], ignore_index=True)
        
        return new_df
    
    def _copy_formatting(self, writer, new_sheet_name: str, original_file: str, 
                        original_sheet: str, status_callback):
        """复制原始文件的格式"""
        try:
            status_callback.emit(Messages.STATUS_COPYING_FORMAT)
            
            # 打开原始文件
            original_wb = openpyxl.load_workbook(original_file)
            original_ws = original_wb[original_sheet]
            
            # 获取新工作表
            new_ws = writer.sheets[new_sheet_name]
            
            # 复制格式
            self._copy_column_widths(original_ws, new_ws)
            self._copy_row_heights(original_ws, new_ws)
            self._copy_cell_formats(original_ws, new_ws)
            
        except Exception as e:
            status_callback.emit(Messages.WARNING_COPY_FORMAT.format(str(e)))
    
    def _copy_column_widths(self, original_ws, new_ws):
        """复制列宽"""
        for col in original_ws.columns:
            col_letter = col[0].column_letter
            if original_ws.column_dimensions[col_letter].width:
                new_ws.column_dimensions[col_letter].width = original_ws.column_dimensions[col_letter].width
    
    def _copy_row_heights(self, original_ws, new_ws):
        """复制行高"""
        for row_num in range(1, min(original_ws.max_row + 1, new_ws.max_row + 1)):
            if original_ws.row_dimensions[row_num].height:
                new_ws.row_dimensions[row_num].height = original_ws.row_dimensions[row_num].height
    
    def _copy_cell_formats(self, original_ws, new_ws):
        """复制单元格格式"""
        if original_ws.max_row > 0:
            for col_num in range(1, original_ws.max_column + 1):
                original_cell = original_ws.cell(row=1, column=col_num)
                new_cell = new_ws.cell(row=1, column=col_num)
                
                # 复制字体
                if original_cell.font:
                    new_cell.font = Font(
                        name=original_cell.font.name,
                        size=original_cell.font.size,
                        bold=original_cell.font.bold,
                        italic=original_cell.font.italic,
                        color=original_cell.font.color
                    )
                
                # 复制填充
                if original_cell.fill:
                    new_cell.fill = PatternFill(
                        fill_type=original_cell.fill.fill_type,
                        start_color=original_cell.fill.start_color,
                        end_color=original_cell.fill.end_color
                    )
                
                # 复制对齐
                if original_cell.alignment:
                    new_cell.alignment = Alignment(
                        horizontal=original_cell.alignment.horizontal,
                        vertical=original_cell.alignment.vertical,
                        wrap_text=original_cell.alignment.wrap_text
                    )
                
                # 复制边框
                if original_cell.border:
                    new_cell.border = original_cell.border
