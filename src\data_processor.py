"""
数据处理模块
负责Excel文件读取、数据汇总和分配逻辑
"""

import os
import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border
from openpyxl.utils.dataframe import dataframe_to_rows
from collections import defaultdict
from typing import List, Dict, Any, Tuple, Optional
from copy import copy
from PyQt6.QtCore import QThread, pyqtSignal

from .config import Config, Messages
from .utils import FileUtils, DataUtils


class DataProcessor(QThread):
    """数据处理线程类"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(str)
    error_signal = pyqtSignal(str)
    
    def __init__(self, file_paths: List[str], customer_service_names: List[str], output_dir: str):
        super().__init__()
        self.file_paths = file_paths
        self.customer_service_names = customer_service_names
        self.output_dir = output_dir
        self.file_reader = ExcelFileReader()
        self.data_aggregator = DataAggregator()
        self.file_generator = OutputFileGenerator()
        
    def run(self):
        """运行数据处理"""
        try:
            self.process_files()
        except Exception as e:
            self.error_signal.emit(Messages.ERROR_PROCESSING.format(str(e)))
    
    def process_files(self):
        """处理文件的主要流程"""
        # 1. 读取文件
        self.status_updated.emit(Messages.STATUS_READING_FILES)
        all_data, file_info = self.file_reader.read_files(
            self.file_paths, 
            self.status_updated, 
            self.progress_updated
        )
        
        if not all_data:
            self.error_signal.emit("没有读取到有效数据")
            return
        
        # 2. 汇总数据
        self.status_updated.emit(Messages.STATUS_SUMMARIZING)
        keyword_summary = self.data_aggregator.aggregate_data(all_data)
        
        # 3. 分配数据
        self.status_updated.emit(Messages.STATUS_ASSIGNING)
        customer_data = self.data_aggregator.distribute_data(
            keyword_summary, 
            self.customer_service_names
        )
        
        self.progress_updated.emit(75)
        
        # 4. 生成输出文件
        self.status_updated.emit(Messages.STATUS_GENERATING)
        self.file_generator.generate_files(
            customer_data, 
            file_info, 
            self.customer_service_names,
            self.output_dir,
            self.status_updated
        )
        
        self.progress_updated.emit(100)
        self.finished_signal.emit(Messages.SUCCESS_PROCESSING_COMPLETE.format(self.output_dir))


class ExcelFileReader:
    """Excel文件读取器"""
    
    def read_files(self, file_paths: List[str], status_callback, progress_callback) -> Tuple[List[Dict], List[Dict]]:
        """读取多个Excel文件"""
        all_data = []
        file_info = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            status_callback.emit(Messages.STATUS_READING_FILE.format(
                i+1, total_files, os.path.basename(file_path)
            ))
            
            try:
                file_data, file_sheets = self._read_single_file(file_path, status_callback)
                all_data.extend(file_data)
                file_info.extend(file_sheets)
                
            except Exception as e:
                status_callback.emit(Messages.ERROR_FILE_READ.format(file_path, str(e)))
                continue
            
            progress = int((i + 1) / total_files * 50)
            progress_callback.emit(progress)
        
        return all_data, file_info
    
    def _read_single_file(self, file_path: str, status_callback) -> Tuple[List[Dict], List[Dict]]:
        """读取单个Excel文件"""
        file_data = []
        file_sheets = []
        
        # 验证文件
        if not Config.validate_file_extension(file_path):
            raise ValueError(Messages.ERROR_INVALID_FILE_FORMAT.format(file_path))
        
        if not Config.validate_file_size(file_path):
            size_mb = Config.get_file_size_mb(file_path)
            raise ValueError(Messages.ERROR_FILE_SIZE_TOO_LARGE.format(
                file_path, size_mb, Config.MAX_FILE_SIZE_MB
            ))
        
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        
        for sheet_name in excel_file.sheet_names:
            status_callback.emit(Messages.STATUS_PROCESSING_SHEET.format(sheet_name))
            
            # 读取工作表数据
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 检查必要的列
            missing_columns = [col for col in Config.REQUIRED_COLUMNS if col not in df.columns]
            if missing_columns:
                status_callback.emit(Messages.ERROR_MISSING_COLUMNS.format(
                    os.path.basename(file_path), sheet_name, missing_columns
                ))
                continue
            
            # 存储数据
            for idx, row in df.iterrows():
                file_data.append({
                    'file_path': file_path,
                    'sheet_name': sheet_name,
                    'row_index': idx,
                    'data': row.to_dict(),
                    'original_df': df
                })
            
            file_sheets.append({
                'file_path': file_path,
                'sheet_name': sheet_name,
                'df': df
            })
        
        return file_data, file_sheets


class DataAggregator:
    """数据汇总器"""
    
    def aggregate_data(self, all_data: List[Dict]) -> Dict[str, Dict]:
        """汇总相同搜索关键词的数据"""
        keyword_summary = defaultdict(lambda: {'total_quantity': 0, 'rows': []})
        
        for item in all_data:
            keyword = item['data'].get('搜索关键词', '')
            quantity = item['data'].get('单数', 0)
            
            if pd.notna(keyword) and pd.notna(quantity) and quantity > 0:
                keyword_summary[keyword]['total_quantity'] += int(quantity)
                keyword_summary[keyword]['rows'].append(item)
        
        return dict(keyword_summary)
    
    def distribute_data(self, keyword_summary: Dict[str, Dict], customer_names: List[str]) -> Dict[str, Dict]:
        """将数据分配给客服 - 按关键词轮流分配，每个关键词只分给一个客服"""
        customer_data = {name: defaultdict(list) for name in customer_names}

        # 将关键词按轮流方式分配给客服
        keywords = list(keyword_summary.keys())
        num_customers = len(customer_names)

        for i, keyword in enumerate(keywords):
            # 轮流分配：第i个关键词分给第(i % num_customers)个客服
            customer_index = i % num_customers
            customer_name = customer_names[customer_index]

            summary = keyword_summary[keyword]

            # 将该关键词的所有原始行都分配给这个客服
            for row in summary['rows']:
                # 完全保持原始行数据不变
                file_key = f"{row['file_path']}_{row['sheet_name']}"
                customer_data[customer_name][file_key].append(row)

        return customer_data


class OutputFileGenerator:
    """输出文件生成器"""
    
    def generate_files(self, customer_data: Dict[str, Dict], file_info: List[Dict], 
                      customer_names: List[str], output_dir: str, status_callback):
        """为每个客服生成Excel文件"""
        for customer_name in customer_names:
            self._create_customer_file(
                customer_name, 
                customer_data[customer_name], 
                file_info, 
                output_dir,
                status_callback
            )
    
    def _create_customer_file(self, customer_name: str, customer_data: Dict,
                             file_info: List[Dict], output_dir: str, status_callback):
        """为单个客服创建Excel文件 - 完整复制原始文件结构"""
        output_file = os.path.join(output_dir, Config.get_output_filename(customer_name))

        # 使用openpyxl直接复制和修改，而不是pandas
        self._create_file_with_openpyxl(customer_name, customer_data, file_info, output_file, status_callback)
    
    def _find_original_info(self, file_sheet_key: str, file_info: List[Dict]) -> Optional[Dict]:
        """查找原始文件信息"""
        for info in file_info:
            if f"{info['file_path']}_{info['sheet_name']}" == file_sheet_key:
                return info
        return None
    
    def _create_customer_dataframe(self, original_df: pd.DataFrame, rows: List[Dict]) -> pd.DataFrame:
        """为客服创建DataFrame - 保持原始表格结构"""
        # 完全复制原始DataFrame，包括所有行和数据
        new_df = original_df.copy()

        # 找到需要拆分的数据行，只修改这些行的"单数"列
        for row_data in rows:
            original_row_index = row_data['row_index']

            # 如果原始行索引在DataFrame范围内，只更新"单数"列
            if original_row_index < len(new_df):
                # 只修改"单数"列，其他列保持原样
                new_df.loc[original_row_index, '单数'] = row_data['data']['单数']
                # 确保搜索关键词也是正确的（虽然应该已经是正确的）
                new_df.loc[original_row_index, '搜索关键词'] = row_data['data']['搜索关键词']

        return new_df

    def _create_file_with_openpyxl(self, customer_name: str, customer_data: Dict,
                                  file_info: List[Dict], output_file: str, status_callback):
        """使用openpyxl创建客服文件，完全保留原始格式和图片"""
        from openpyxl import load_workbook
        import shutil

        # 收集所有需要保留的行索引
        rows_to_keep = set()
        original_files_used = set()

        for file_sheet_key, rows in customer_data.items():
            if not rows:
                continue

            original_info = self._find_original_info(file_sheet_key, file_info)
            if not original_info:
                continue

            original_files_used.add(original_info['file_path'])

            # 收集该客服应该保留的行
            for row_data in rows:
                rows_to_keep.add((original_info['file_path'], original_info['sheet_name'], row_data['row_index']))

        # 处理每个原始文件
        for original_file in original_files_used:
            self._process_original_file(original_file, rows_to_keep, output_file, customer_name, status_callback)

        status_callback.emit(f"已创建客服文件: {customer_name}")

    def _process_original_file(self, original_file: str, rows_to_keep: set,
                              output_file: str, customer_name: str, status_callback):
        """处理单个原始文件，只保留指定的行"""
        from openpyxl import load_workbook
        import shutil

        # 如果是第一个文件，直接复制整个文件
        if not os.path.exists(output_file):
            shutil.copy2(original_file, output_file)

            # 删除不需要的数据行，但保留所有格式和图片
            wb = load_workbook(output_file)

            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                self._filter_sheet_rows(ws, original_file, sheet_name, rows_to_keep)

            wb.save(output_file)
            wb.close()
        else:
            # 如果输出文件已存在，需要合并工作表
            wb_output = load_workbook(output_file)
            wb_source = load_workbook(original_file)

            for sheet_name in wb_source.sheetnames:
                # 创建新的工作表名（避免冲突）
                new_sheet_name = f"{sheet_name}_{os.path.basename(original_file).replace('.xlsx', '')}"
                if new_sheet_name in wb_output.sheetnames:
                    new_sheet_name = f"{new_sheet_name}_{customer_name}"

                # 完全复制工作表（包括所有格式、图片、图表等）
                source_sheet = wb_source[sheet_name]
                target_sheet = wb_output.copy_worksheet(source_sheet)
                target_sheet.title = new_sheet_name

                # 过滤行数据
                self._filter_sheet_rows(target_sheet, original_file, sheet_name, rows_to_keep)

            wb_output.save(output_file)
            wb_source.close()
            wb_output.close()

    def _filter_sheet_rows(self, worksheet, original_file: str, sheet_name: str, rows_to_keep: set):
        """过滤工作表行，只保留指定的数据行，但保留表头"""
        # 找到数据开始的行（通常是第2行，第1行是表头）
        data_start_row = 2
        max_row = worksheet.max_row

        # 从后往前删除不需要的行（避免索引变化问题）
        rows_to_delete = []

        for row_num in range(data_start_row, max_row + 1):
            # 检查这一行是否应该保留（row_index是从0开始的，所以要减2）
            row_index = row_num - 2
            should_keep = (original_file, sheet_name, row_index) in rows_to_keep

            if not should_keep:
                rows_to_delete.append(row_num)

        # 从后往前删除行
        for row_num in reversed(rows_to_delete):
            worksheet.delete_rows(row_num)

    def _update_sheet_data(self, worksheet, rows: List[Dict], original_df: pd.DataFrame):
        """更新工作表中的数据行"""
        # 找到"单数"列的位置
        header_row = 1  # 假设第一行是表头
        quantity_col = None

        # 查找"单数"列
        for col in range(1, worksheet.max_column + 1):
            cell_value = worksheet.cell(row=header_row, column=col).value
            if cell_value == '单数':
                quantity_col = col
                break

        if quantity_col is None:
            return  # 如果找不到"单数"列，跳过

        # 更新每一行的"单数"值
        for row_data in rows:
            excel_row = row_data['row_index'] + 2  # +2 因为Excel从1开始，且第1行是表头
            new_quantity = row_data['data']['单数']

            # 更新"单数"列的值
            worksheet.cell(row=excel_row, column=quantity_col, value=new_quantity)
    
    def _copy_formatting(self, writer, new_sheet_name: str, original_file: str,
                        original_sheet: str, status_callback):
        """复制原始文件的格式（安全处理合并单元格）"""
        try:
            status_callback.emit(Messages.STATUS_COPYING_FORMAT)

            # 打开原始文件
            original_wb = openpyxl.load_workbook(original_file, data_only=False)
            original_ws = original_wb[original_sheet]

            # 获取新工作表
            new_ws = writer.sheets[new_sheet_name]

            # 安全复制格式
            self._safe_copy_formatting(original_ws, new_ws, status_callback)

        except Exception as e:
            status_callback.emit(f"格式复制警告: {str(e)}")

    def _safe_copy_formatting(self, original_ws, new_ws, status_callback):
        """安全地复制格式，避免合并单元格问题"""
        try:
            # 1. 复制列宽
            self._copy_column_widths(original_ws, new_ws)

            # 2. 复制行高
            self._copy_row_heights(original_ws, new_ws)

            # 3. 复制单元格格式（只复制表头）
            self._copy_cell_formats(original_ws, new_ws)

            # 4. 复制合并单元格（只在表头区域）
            self._copy_merged_cells(original_ws, new_ws)

        except Exception as e:
            status_callback.emit(f"部分格式复制失败: {str(e)}")

    def _copy_merged_cells(self, original_ws, new_ws):
        """复制合并单元格（只复制表头区域的）"""
        try:
            for merged_range in original_ws.merged_cells.ranges:
                # 只复制第一行的合并单元格（表头）
                if merged_range.min_row == 1 and merged_range.max_row == 1:
                    # 确保合并范围在新工作表的范围内
                    if merged_range.max_col <= new_ws.max_column:
                        new_ws.merge_cells(str(merged_range))
        except Exception as e:
            # 合并单元格复制失败不影响主要功能
            pass
    
    def _copy_column_widths(self, original_ws, new_ws):
        """复制列宽"""
        try:
            for col_letter in original_ws.column_dimensions:
                if original_ws.column_dimensions[col_letter].width:
                    new_ws.column_dimensions[col_letter].width = original_ws.column_dimensions[col_letter].width
        except Exception as e:
            # 如果复制列宽失败，跳过但不影响主要功能
            pass
    
    def _copy_row_heights(self, original_ws, new_ws):
        """复制行高"""
        try:
            for row_num in range(1, min(original_ws.max_row + 1, new_ws.max_row + 1)):
                if original_ws.row_dimensions[row_num].height:
                    new_ws.row_dimensions[row_num].height = original_ws.row_dimensions[row_num].height
        except Exception as e:
            # 如果复制行高失败，跳过但不影响主要功能
            pass
    
    def _copy_cell_formats(self, original_ws, new_ws):
        """复制单元格格式（处理合并单元格）"""
        try:
            if original_ws.max_row > 0:
                # 只复制表头行的格式
                for col_num in range(1, min(original_ws.max_column + 1, new_ws.max_column + 1)):
                    try:
                        original_cell = original_ws.cell(row=1, column=col_num)
                        new_cell = new_ws.cell(row=1, column=col_num)

                        # 跳过合并单元格
                        if hasattr(original_cell, 'coordinate') and original_cell.coordinate in original_ws.merged_cells:
                            continue

                        # 复制字体
                        if hasattr(original_cell, 'font') and original_cell.font:
                            try:
                                new_cell.font = Font(
                                    name=original_cell.font.name,
                                    size=original_cell.font.size,
                                    bold=original_cell.font.bold,
                                    italic=original_cell.font.italic,
                                    color=original_cell.font.color
                                )
                            except:
                                pass

                        # 复制填充
                        if hasattr(original_cell, 'fill') and original_cell.fill:
                            try:
                                new_cell.fill = PatternFill(
                                    fill_type=original_cell.fill.fill_type,
                                    start_color=original_cell.fill.start_color,
                                    end_color=original_cell.fill.end_color
                                )
                            except:
                                pass

                        # 复制对齐
                        if hasattr(original_cell, 'alignment') and original_cell.alignment:
                            try:
                                new_cell.alignment = Alignment(
                                    horizontal=original_cell.alignment.horizontal,
                                    vertical=original_cell.alignment.vertical,
                                    wrap_text=original_cell.alignment.wrap_text
                                )
                            except:
                                pass

                        # 复制边框
                        if hasattr(original_cell, 'border') and original_cell.border:
                            try:
                                new_cell.border = original_cell.border
                            except:
                                pass

                    except Exception as e:
                        # 跳过有问题的单元格，继续处理下一个
                        continue

        except Exception as e:
            # 如果整个格式复制失败，跳过但不影响主要功能
            pass
