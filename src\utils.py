"""
工具函数模块
包含通用的工具函数和辅助类
"""

import os
import shutil
from typing import List, Optional, Tuple
from datetime import datetime
import pandas as pd


class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def ensure_directory_exists(directory: str) -> None:
        """确保目录存在"""
        os.makedirs(directory, exist_ok=True)
    
    @staticmethod
    def get_safe_filename(filename: str) -> str:
        """获取安全的文件名（移除非法字符）"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename
    
    @staticmethod
    def backup_file(file_path: str, backup_dir: str = None) -> Optional[str]:
        """备份文件"""
        if not os.path.exists(file_path):
            return None
        
        if backup_dir is None:
            backup_dir = os.path.join(os.path.dirname(file_path), 'backup')
        
        FileUtils.ensure_directory_exists(backup_dir)
        
        # 生成备份文件名
        base_name = os.path.basename(file_path)
        name, ext = os.path.splitext(base_name)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{name}_backup_{timestamp}{ext}"
        backup_path = os.path.join(backup_dir, backup_name)
        
        try:
            shutil.copy2(file_path, backup_path)
            return backup_path
        except Exception:
            return None
    
    @staticmethod
    def get_file_info(file_path: str) -> dict:
        """获取文件信息"""
        if not os.path.exists(file_path):
            return {}
        
        stat = os.stat(file_path)
        return {
            'size': stat.st_size,
            'size_mb': stat.st_size / (1024 * 1024),
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'created': datetime.fromtimestamp(stat.st_ctime),
            'extension': os.path.splitext(file_path)[1].lower()
        }
    
    @staticmethod
    def validate_excel_file(file_path: str) -> Tuple[bool, str]:
        """验证Excel文件"""
        if not os.path.exists(file_path):
            return False, "文件不存在"
        
        file_info = FileUtils.get_file_info(file_path)
        
        # 检查文件扩展名
        if file_info.get('extension') not in ['.xlsx', '.xls']:
            return False, "不支持的文件格式"
        
        # 检查文件大小
        if file_info.get('size_mb', 0) > 100:
            return False, f"文件太大 ({file_info.get('size_mb', 0):.1f}MB)"
        
        # 尝试读取文件
        try:
            pd.ExcelFile(file_path)
            return True, "文件有效"
        except Exception as e:
            return False, f"文件损坏或无法读取: {str(e)}"


class DataUtils:
    """数据处理工具类"""
    
    @staticmethod
    def distribute_quantity(total_quantity: int, num_recipients: int) -> List[int]:
        """将总数量平均分配给指定数量的接收者"""
        if num_recipients <= 0:
            return []
        
        if total_quantity <= 0:
            return [0] * num_recipients
        
        # 计算基础分配量和余数
        base_quantity = total_quantity // num_recipients
        remainder = total_quantity % num_recipients
        
        # 分配数量
        quantities = [base_quantity] * num_recipients
        
        # 将余数分配给前面的接收者
        for i in range(remainder):
            quantities[i] += 1
        
        return quantities
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清理文本（移除多余空格等）"""
        if not isinstance(text, str):
            return str(text) if text is not None else ""
        
        return text.strip().replace('\n', ' ').replace('\r', ' ')
    
    @staticmethod
    def is_valid_quantity(value) -> bool:
        """检查是否为有效的数量值"""
        if pd.isna(value):
            return False
        
        try:
            num = float(value)
            return num > 0 and num == int(num)  # 必须是正整数
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def safe_int(value, default: int = 0) -> int:
        """安全转换为整数"""
        try:
            if pd.isna(value):
                return default
            return int(float(value))
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def group_by_keyword(data_list: List[dict], keyword_field: str = '搜索关键词') -> dict:
        """按关键词分组数据"""
        groups = {}
        
        for item in data_list:
            keyword = item.get(keyword_field, '')
            if keyword:
                keyword = DataUtils.clean_text(keyword)
                if keyword not in groups:
                    groups[keyword] = []
                groups[keyword].append(item)
        
        return groups
    
    @staticmethod
    def calculate_distribution_stats(keyword_summary: dict, num_customers: int) -> dict:
        """计算分配统计信息"""
        stats = {
            'total_keywords': len(keyword_summary),
            'total_quantity': 0,
            'distribution_preview': {}
        }
        
        for keyword, summary in keyword_summary.items():
            total_qty = summary.get('total_quantity', 0)
            stats['total_quantity'] += total_qty
            
            # 计算分配预览
            distribution = DataUtils.distribute_quantity(total_qty, num_customers)
            stats['distribution_preview'][keyword] = {
                'total': total_qty,
                'distribution': distribution,
                'per_customer': distribution
            }
        
        return stats


class ValidationUtils:
    """验证工具类"""
    
    @staticmethod
    def validate_customer_name(name: str) -> Tuple[bool, str]:
        """验证客服名称"""
        if not name or not name.strip():
            return False, "客服名称不能为空"
        
        name = name.strip()
        
        if len(name) > 50:
            return False, "客服名称太长（最多50个字符）"
        
        # 检查非法字符
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            if char in name:
                return False, f"客服名称不能包含字符: {char}"
        
        return True, "名称有效"
    
    @staticmethod
    def validate_file_list(file_paths: List[str]) -> Tuple[bool, str, List[str]]:
        """验证文件列表"""
        if not file_paths:
            return False, "请选择至少一个文件", []
        
        valid_files = []
        invalid_files = []
        
        for file_path in file_paths:
            is_valid, message = FileUtils.validate_excel_file(file_path)
            if is_valid:
                valid_files.append(file_path)
            else:
                invalid_files.append(f"{os.path.basename(file_path)}: {message}")
        
        if not valid_files:
            return False, "没有有效的Excel文件", invalid_files
        
        if invalid_files:
            warning_msg = f"发现 {len(invalid_files)} 个无效文件，将跳过处理"
            return True, warning_msg, invalid_files
        
        return True, f"所有 {len(valid_files)} 个文件都有效", []
    
    @staticmethod
    def validate_output_directory(output_dir: str) -> Tuple[bool, str]:
        """验证输出目录"""
        if not output_dir or not output_dir.strip():
            return False, "请选择输出目录"
        
        try:
            # 尝试创建目录（如果不存在）
            FileUtils.ensure_directory_exists(output_dir)
            
            # 检查写入权限
            test_file = os.path.join(output_dir, 'test_write_permission.tmp')
            try:
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                return True, "输出目录有效"
            except Exception:
                return False, "输出目录没有写入权限"
                
        except Exception as e:
            return False, f"无法创建输出目录: {str(e)}"


class LogUtils:
    """日志工具类"""
    
    @staticmethod
    def format_processing_log(file_count: int, customer_count: int, 
                            keyword_count: int, total_quantity: int) -> str:
        """格式化处理日志"""
        return (
            f"处理摘要:\n"
            f"- 处理文件数: {file_count}\n"
            f"- 客服数量: {customer_count}\n"
            f"- 关键词数量: {keyword_count}\n"
            f"- 总单数: {total_quantity}\n"
            f"- 平均每客服: {total_quantity // customer_count if customer_count > 0 else 0} 单"
        )
    
    @staticmethod
    def format_error_log(error_type: str, details: str, file_path: str = None) -> str:
        """格式化错误日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {error_type}: {details}"
        
        if file_path:
            log_entry += f" (文件: {os.path.basename(file_path)})"
        
        return log_entry
