"""
运营表格数据自动拆分程序 - 主入口文件
"""

import sys
import os
from PyQt6.QtWidgets import QApplication

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui import MainWindow
from src.config import Config


def main():
    """程序主入口"""
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName(Config.APP_NAME)
    app.setApplicationVersion(Config.APP_VERSION)

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
