import sys
import os
from PyQt6.QtWidgets import (QA<PERSON>lication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QFileDialog, QTextEdit,
                             QListWidget, QSpinBox, QMessageBox, QProgressBar,
                             QGroupBox, QGridLayout, QLineEdit, QComboBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont
import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border
from openpyxl.utils.dataframe import dataframe_to_rows
import math
from collections import defaultdict

class DataProcessor(QThread):
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(str)
    error_signal = pyqtSignal(str)
    
    def __init__(self, file_paths, customer_service_names, output_dir):
        super().__init__()
        self.file_paths = file_paths
        self.customer_service_names = customer_service_names
        self.output_dir = output_dir
        
    def run(self):
        try:
            self.process_files()
        except Exception as e:
            self.error_signal.emit(f"处理过程中出现错误: {str(e)}")
    
    def process_files(self):
        self.status_updated.emit("开始读取文件...")
        
        # 存储所有数据
        all_data = []
        file_info = []
        
        total_files = len(self.file_paths)
        
        for i, file_path in enumerate(self.file_paths):
            self.status_updated.emit(f"正在读取文件 {i+1}/{total_files}: {os.path.basename(file_path)}")
            
            try:
                # 读取Excel文件的所有sheet
                excel_file = pd.ExcelFile(file_path)
                
                for sheet_name in excel_file.sheet_names:
                    self.status_updated.emit(f"正在处理 {sheet_name} 工作表...")
                    
                    # 读取数据
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    # 检查必要的列是否存在
                    required_columns = ['搜索主图', '商品ID', '搜索关键词', '单数', '合计单量', '需求备注']
                    missing_columns = [col for col in required_columns if col not in df.columns]
                    
                    if missing_columns:
                        self.status_updated.emit(f"警告: {sheet_name} 缺少列: {missing_columns}")
                        continue
                    
                    # 存储原始数据和文件信息
                    for idx, row in df.iterrows():
                        all_data.append({
                            'file_path': file_path,
                            'sheet_name': sheet_name,
                            'row_index': idx,
                            'data': row.to_dict(),
                            'original_df': df
                        })
                    
                    file_info.append({
                        'file_path': file_path,
                        'sheet_name': sheet_name,
                        'df': df
                    })
                        
            except Exception as e:
                self.error_signal.emit(f"读取文件 {file_path} 时出错: {str(e)}")
                return
            
            progress = int((i + 1) / total_files * 50)
            self.progress_updated.emit(progress)
        
        self.status_updated.emit("开始汇总和拆分数据...")
        
        # 汇总相同搜索关键词的数据
        keyword_summary = defaultdict(lambda: {'total_quantity': 0, 'rows': []})
        
        for item in all_data:
            keyword = item['data'].get('搜索关键词', '')
            quantity = item['data'].get('单数', 0)
            
            if pd.notna(keyword) and pd.notna(quantity) and quantity > 0:
                keyword_summary[keyword]['total_quantity'] += int(quantity)
                keyword_summary[keyword]['rows'].append(item)
        
        self.status_updated.emit("开始分配给客服...")
        
        # 为每个客服创建数据
        customer_data = {name: defaultdict(list) for name in self.customer_service_names}
        
        for keyword, summary in keyword_summary.items():
            total_quantity = summary['total_quantity']
            num_customers = len(self.customer_service_names)
            
            # 计算每个客服分配的数量
            base_quantity = total_quantity // num_customers
            extra_quantity = total_quantity % num_customers
            
            # 分配数量
            quantities = [base_quantity] * num_customers
            for i in range(extra_quantity):
                quantities[i] += 1
            
            # 为每个客服分配数据
            for i, customer_name in enumerate(self.customer_service_names):
                if quantities[i] > 0:
                    # 复制第一行数据作为模板
                    template_row = summary['rows'][0].copy()
                    template_row['data'] = template_row['data'].copy()
                    template_row['data']['单数'] = quantities[i]
                    
                    file_key = f"{template_row['file_path']}_{template_row['sheet_name']}"
                    customer_data[customer_name][file_key].append(template_row)
        
        self.progress_updated.emit(75)
        
        # 生成输出文件
        self.status_updated.emit("正在生成输出文件...")
        
        for customer_name in self.customer_service_names:
            self.create_customer_file(customer_name, customer_data[customer_name], file_info)
        
        self.progress_updated.emit(100)
        self.finished_signal.emit(f"处理完成！文件已保存到: {self.output_dir}")
    
    def create_customer_file(self, customer_name, customer_data, file_info):
        """为每个客服创建Excel文件"""
        output_file = os.path.join(self.output_dir, f"{customer_name}_运营表格.xlsx")
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            for file_sheet_key, rows in customer_data.items():
                if not rows:
                    continue
                
                # 获取原始文件信息
                original_info = None
                for info in file_info:
                    if f"{info['file_path']}_{info['sheet_name']}" == file_sheet_key:
                        original_info = info
                        break
                
                if not original_info:
                    continue
                
                # 创建新的DataFrame
                new_df = original_info['df'].copy()
                
                # 清空数据行，只保留表头
                new_df = new_df.iloc[:0].copy()
                
                # 添加客服的数据
                for row_data in rows:
                    new_df = pd.concat([new_df, pd.DataFrame([row_data['data']])], ignore_index=True)
                
                # 写入Excel
                sheet_name = original_info['sheet_name']
                new_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 复制原始格式
                self.copy_formatting(writer, sheet_name, original_info['file_path'], original_info['sheet_name'])
    
    def copy_formatting(self, writer, new_sheet_name, original_file, original_sheet):
        """复制原始文件的格式"""
        try:
            # 打开原始文件
            original_wb = openpyxl.load_workbook(original_file)
            original_ws = original_wb[original_sheet]
            
            # 获取新工作表
            new_ws = writer.sheets[new_sheet_name]
            
            # 复制列宽
            for col in original_ws.columns:
                col_letter = col[0].column_letter
                if original_ws.column_dimensions[col_letter].width:
                    new_ws.column_dimensions[col_letter].width = original_ws.column_dimensions[col_letter].width
            
            # 复制行高
            for row_num in range(1, min(original_ws.max_row + 1, new_ws.max_row + 1)):
                if original_ws.row_dimensions[row_num].height:
                    new_ws.row_dimensions[row_num].height = original_ws.row_dimensions[row_num].height
            
            # 复制表头格式
            if original_ws.max_row > 0:
                for col_num in range(1, original_ws.max_column + 1):
                    original_cell = original_ws.cell(row=1, column=col_num)
                    new_cell = new_ws.cell(row=1, column=col_num)
                    
                    # 复制字体
                    if original_cell.font:
                        new_cell.font = Font(
                            name=original_cell.font.name,
                            size=original_cell.font.size,
                            bold=original_cell.font.bold,
                            italic=original_cell.font.italic,
                            color=original_cell.font.color
                        )
                    
                    # 复制填充
                    if original_cell.fill:
                        new_cell.fill = PatternFill(
                            fill_type=original_cell.fill.fill_type,
                            start_color=original_cell.fill.start_color,
                            end_color=original_cell.fill.end_color
                        )
                    
                    # 复制对齐
                    if original_cell.alignment:
                        new_cell.alignment = Alignment(
                            horizontal=original_cell.alignment.horizontal,
                            vertical=original_cell.alignment.vertical,
                            wrap_text=original_cell.alignment.wrap_text
                        )
                    
                    # 复制边框
                    if original_cell.border:
                        new_cell.border = original_cell.border
                        
        except Exception as e:
            self.status_updated.emit(f"复制格式时出现警告: {str(e)}")


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.file_paths = []
        self.customer_service_names = []
        self.output_dir = ""
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("运营表格数据拆分工具")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("运营表格数据自动拆分程序")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 文件选择区域
        file_group = QGroupBox("1. 选择Excel文件")
        file_layout = QVBoxLayout(file_group)
        
        file_button_layout = QHBoxLayout()
        self.select_files_btn = QPushButton("选择Excel文件")
        self.select_files_btn.clicked.connect(self.select_files)
        file_button_layout.addWidget(self.select_files_btn)
        
        self.clear_files_btn = QPushButton("清空文件列表")
        self.clear_files_btn.clicked.connect(self.clear_files)
        file_button_layout.addWidget(self.clear_files_btn)
        file_layout.addLayout(file_button_layout)
        
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(100)
        file_layout.addWidget(self.file_list)
        
        main_layout.addWidget(file_group)
        
        # 客服设置区域
        customer_group = QGroupBox("2. 客服设置")
        customer_layout = QGridLayout(customer_group)
        
        customer_layout.addWidget(QLabel("客服姓名:"), 0, 0)
        self.customer_input = QLineEdit()
        self.customer_input.setPlaceholderText("输入客服姓名，按回车添加")
        self.customer_input.returnPressed.connect(self.add_customer)
        customer_layout.addWidget(self.customer_input, 0, 1)
        
        self.add_customer_btn = QPushButton("添加客服")
        self.add_customer_btn.clicked.connect(self.add_customer)
        customer_layout.addWidget(self.add_customer_btn, 0, 2)
        
        self.customer_list = QListWidget()
        self.customer_list.setMaximumHeight(100)
        customer_layout.addWidget(self.customer_list, 1, 0, 1, 3)
        
        remove_customer_layout = QHBoxLayout()
        self.remove_customer_btn = QPushButton("删除选中客服")
        self.remove_customer_btn.clicked.connect(self.remove_customer)
        remove_customer_layout.addWidget(self.remove_customer_btn)
        
        self.clear_customers_btn = QPushButton("清空客服列表")
        self.clear_customers_btn.clicked.connect(self.clear_customers)
        remove_customer_layout.addWidget(self.clear_customers_btn)
        
        customer_layout.addLayout(remove_customer_layout, 2, 0, 1, 3)
        
        main_layout.addWidget(customer_group)
        
        # 输出设置区域
        output_group = QGroupBox("3. 输出设置")
        output_layout = QHBoxLayout(output_group)
        
        output_layout.addWidget(QLabel("输出目录:"))
        self.output_label = QLabel("未选择")
        self.output_label.setStyleSheet("QLabel { border: 1px solid gray; padding: 5px; }")
        output_layout.addWidget(self.output_label)
        
        self.select_output_btn = QPushButton("选择输出目录")
        self.select_output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(self.select_output_btn)
        
        main_layout.addWidget(output_group)
        
        # 处理按钮
        self.process_btn = QPushButton("开始处理")
        self.process_btn.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setMinimumHeight(40)
        main_layout.addWidget(self.process_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 状态显示
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        main_layout.addWidget(self.status_text)
        
        # 添加默认客服名称
        default_customers = ["客服A", "客服B", "客服C", "客服D", "客服E"]
        for customer in default_customers:
            self.customer_service_names.append(customer)
            self.customer_list.addItem(customer)
    
    def select_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择Excel文件", "", "Excel files (*.xlsx *.xls)"
        )
        
        for file in files:
            if file not in self.file_paths:
                self.file_paths.append(file)
                self.file_list.addItem(os.path.basename(file))
    
    def clear_files(self):
        self.file_paths.clear()
        self.file_list.clear()
    
    def add_customer(self):
        customer_name = self.customer_input.text().strip()
        if customer_name and customer_name not in self.customer_service_names:
            self.customer_service_names.append(customer_name)
            self.customer_list.addItem(customer_name)
            self.customer_input.clear()
    
    def remove_customer(self):
        current_row = self.customer_list.currentRow()
        if current_row >= 0:
            item = self.customer_list.takeItem(current_row)
            self.customer_service_names.remove(item.text())
    
    def clear_customers(self):
        self.customer_service_names.clear()
        self.customer_list.clear()
    
    def select_output_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_label.setText(dir_path)
    
    def start_processing(self):
        # 验证输入
        if not self.file_paths:
            QMessageBox.warning(self, "警告", "请选择至少一个Excel文件！")
            return
        
        if not self.customer_service_names:
            QMessageBox.warning(self, "警告", "请添加至少一个客服！")
            return
        
        if not self.output_dir:
            QMessageBox.warning(self, "警告", "请选择输出目录！")
            return
        
        # 禁用按钮，显示进度条
        self.process_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_text.clear()
        
        # 创建处理线程
        self.processor = DataProcessor(self.file_paths, self.customer_service_names, self.output_dir)
        self.processor.progress_updated.connect(self.progress_bar.setValue)
        self.processor.status_updated.connect(self.update_status)
        self.processor.finished_signal.connect(self.processing_finished)
        self.processor.error_signal.connect(self.processing_error)
        self.processor.start()
    
    def update_status(self, message):
        self.status_text.append(message)
        self.status_text.ensureCursorVisible()
    
    def processing_finished(self, message):
        self.update_status(message)
        self.process_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        QMessageBox.information(self, "完成", message)
    
    def processing_error(self, error_message):
        self.update_status(f"错误: {error_message}")
        self.process_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "错误", error_message)


def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
