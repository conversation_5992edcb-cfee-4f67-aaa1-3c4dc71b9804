# 运营表格数据自动拆分程序

这是一个基于PyQt6开发的自动拆分运营表格数据的工具，可以将搜索关键词和单数按照客服数量平均分配。

## 功能特点

1. **多文件支持**: 可以同时处理多个Excel文件
2. **多Sheet支持**: 自动读取每个Excel文件中的所有工作表
3. **智能汇总**: 自动汇总相同搜索关键词的单数
4. **平均分配**: 将汇总后的单数平均分配给不同的客服
5. **格式保持**: 保持原有的表格样式和格式不变
6. **批量处理**: 支持批量处理多个文件

## 安装要求

### 系统要求
- Python 3.8 或更高版本
- Windows/macOS/Linux

### 依赖包
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install PyQt6==6.6.1 pandas==2.1.4 openpyxl==3.1.2 xlrd==2.0.1
```

## 使用方法

### 1. 启动程序
```bash
python main.py
```

### 2. 操作步骤

#### 步骤1: 选择Excel文件
- 点击"选择Excel文件"按钮
- 选择一个或多个包含运营数据的Excel文件
- 支持.xlsx和.xls格式

#### 步骤2: 设置客服
- 在"客服姓名"输入框中输入客服名称
- 点击"添加客服"或按回车键添加
- 程序默认已添加5个客服（客服A-E）
- 可以删除不需要的客服或添加新的客服

#### 步骤3: 选择输出目录
- 点击"选择输出目录"按钮
- 选择保存拆分后文件的目录

#### 步骤4: 开始处理
- 点击"开始处理"按钮
- 程序会显示处理进度和状态信息
- 处理完成后会在输出目录生成对应的文件

## 表格格式要求

输入的Excel文件必须包含以下列：
- **搜索主图**: 商品主图信息
- **商品ID**: 商品的唯一标识
- **搜索关键词**: 用于搜索的关键词
- **单数**: 需要分配的数量
- **合计单量**: 总计数量
- **需求备注**: 备注信息

## 处理逻辑

1. **数据读取**: 程序会读取所有选中的Excel文件和其中的所有工作表
2. **关键词汇总**: 将相同搜索关键词的单数进行汇总
3. **平均分配**: 将汇总后的单数平均分配给所有客服
   - 如果无法整除，前面的客服会多分配1单
   - 例如：10单分给3个客服，分配结果为 4、3、3
4. **文件生成**: 为每个客服生成一个独立的Excel文件
5. **格式保持**: 保持原有的表格格式、样式和布局

## 输出结果

程序会在指定的输出目录中为每个客服生成一个Excel文件：
- 文件命名格式：`{客服名称}_运营表格.xlsx`
- 每个文件包含该客服分配到的所有数据
- 保持原有的工作表结构和格式

## 示例

假设有以下数据：
- 搜索关键词：狐狸肛塞尾巴女用玩具情趣肛门自慰器男同菊花前列腺按摩后庭拉珠
- 单数：10
- 客服数量：5个

分配结果：每个客服分配2单

## 注意事项

1. 确保Excel文件包含所有必需的列
2. 单数列必须是数字格式
3. 程序会跳过单数为0或空的行
4. 建议在处理前备份原始文件
5. 输出目录需要有写入权限

## 故障排除

### 常见问题

1. **"缺少列"错误**
   - 检查Excel文件是否包含所有必需的列
   - 确保列名完全匹配（区分大小写）

2. **"读取文件时出错"**
   - 确保文件没有被其他程序占用
   - 检查文件是否损坏
   - 确认文件格式正确

3. **程序无法启动**
   - 检查Python版本是否符合要求
   - 确认所有依赖包已正确安装

### 技术支持

如果遇到问题，请检查：
1. Python和依赖包版本
2. 文件格式和内容
3. 系统权限设置

## 更新日志

### v1.0.0
- 初始版本发布
- 支持多文件和多Sheet处理
- 实现关键词汇总和平均分配
- 保持原有格式和样式
