"""
测试格式复制修复
创建包含合并单元格的测试文件，验证程序是否能正确处理
"""

import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment
import os

def create_test_file_with_merged_cells():
    """创建包含合并单元格的测试Excel文件"""
    
    # 创建测试数据
    data = [
        {
            '搜索主图': 'image1.jpg',
            '商品ID': 'SP001',
            '搜索关键词': '测试关键词1',
            '单数': 5,
            '合计单量': 20,
            '需求备注': '测试备注1'
        },
        {
            '搜索主图': 'image2.jpg',
            '商品ID': 'SP002', 
            '搜索关键词': '测试关键词2',
            '单数': 8,
            '合计单量': 30,
            '需求备注': '测试备注2'
        },
        {
            '搜索主图': 'image3.jpg',
            '商品ID': 'SP003',
            '搜索关键词': '测试关键词1',
            '单数': 3,
            '合计单量': 20,
            '需求备注': '测试备注3'
        }
    ]
    
    # 确保目录存在
    os.makedirs('sample_data', exist_ok=True)
    
    # 创建Excel文件
    file_path = 'sample_data/测试合并单元格.xlsx'
    df = pd.DataFrame(data)
    
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='测试数据', index=False)
        
        # 获取工作表
        worksheet = writer.sheets['测试数据']
        
        # 添加一些格式和合并单元格
        try:
            # 设置表头样式
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_font = Font(color="FFFFFF", bold=True)
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            for col in range(1, 7):  # A到F列
                cell = worksheet.cell(row=1, column=col)
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
            
            # 合并一些单元格（模拟复杂表头）
            # 注意：这里故意创建一些合并单元格来测试我们的修复
            worksheet.merge_cells('A1:B1')  # 合并搜索主图和商品ID的表头
            worksheet['A1'] = '商品信息'
            
            # 设置列宽
            worksheet.column_dimensions['A'].width = 15
            worksheet.column_dimensions['B'].width = 12
            worksheet.column_dimensions['C'].width = 30
            worksheet.column_dimensions['D'].width = 8
            worksheet.column_dimensions['E'].width = 10
            worksheet.column_dimensions['F'].width = 20
            
            print(f"✓ 创建了包含合并单元格的测试文件: {file_path}")
            
        except Exception as e:
            print(f"⚠ 创建格式时出现问题: {e}")
    
    return file_path

def test_program_with_merged_cells():
    """测试程序是否能处理包含合并单元格的文件"""
    
    print("开始测试合并单元格处理...")
    
    # 创建测试文件
    test_file = create_test_file_with_merged_cells()
    
    print(f"测试文件已创建: {test_file}")
    print("\n现在可以用这个文件测试程序:")
    print("1. 运行 python main.py")
    print("2. 选择刚创建的测试文件")
    print("3. 设置客服（默认已有5个）")
    print("4. 选择输出目录")
    print("5. 点击开始处理")
    print("\n如果程序能正常运行而不出现合并单元格错误，说明修复成功！")
    
    # 显示预期结果
    print("\n预期处理结果:")
    print("- 测试关键词1: 总单数8 (5+3)，分配给5个客服: [2,2,2,1,1]")
    print("- 测试关键词2: 总单数8，分配给5个客服: [2,2,2,1,1]")
    print("- 其他列（搜索主图、商品ID、合计单量、需求备注）保持原样")

if __name__ == "__main__":
    test_program_with_merged_cells()
